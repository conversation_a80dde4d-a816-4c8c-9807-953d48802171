import 'package:devfolio/Core/Utils/Reusable/about_point.dart';
import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:devfolio/Core/Utils/Reusable/section_container.dart';
import 'package:devfolio/Core/Utils/Reusable/skill_chip.dart';
import 'package:flutter/material.dart';
import '../../Core/layout/responsive_layout.dart';

class AboutScreen extends StatelessWidget {
  const AboutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return SectionContainer(
      title: 'About Me',
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // About Description
            CustomText(
              text:
                  'I am a passionate Flutter developer with over 3 years of experience in mobile app development. I specialize in creating cross-platform applications that provide excellent user experiences.',
              fontSize: ResponsiveLayout.getBodyFontSize(context),
              color: Colors.grey,
            ),

            SizedBox(height: ResponsiveLayout.getSectionSpacing(context)),

            // Key Points
            AboutPoint(
              title: 'Mobile Development',
              description:
                  'Expert in Flutter and Dart for cross-platform mobile development',
              icon: Icons.phone_android,
            ),

            SizedBox(height: ResponsiveLayout.getMediumSpacing(context)),

            AboutPoint(
              title: 'UI/UX Design',
              description:
                  'Creating beautiful and intuitive user interfaces with modern design principles',
              icon: Icons.design_services,
            ),

            SizedBox(height: ResponsiveLayout.getMediumSpacing(context)),

            AboutPoint(
              title: 'Problem Solving',
              description:
                  'Strong analytical skills to solve complex technical challenges',
              icon: Icons.lightbulb,
            ),

            SizedBox(height: ResponsiveLayout.getSectionSpacing(context)),

            // Skills Preview
            CustomText(
              text: 'Key Skills',
              fontSize: ResponsiveLayout.getSubtitleFontSize(context),
              fontWeight: FontWeight.bold,
            ),

            SizedBox(height: ResponsiveLayout.getSmallSpacing(context)),

            Wrap(
              spacing: ResponsiveLayout.getSmallSpacing(context),
              runSpacing: ResponsiveLayout.getSmallSpacing(context),
              children: [
                SkillChip(skill: 'Flutter'),
                SkillChip(skill: 'Dart'),
                SkillChip(skill: 'Firebase'),
                SkillChip(skill: 'REST APIs'),
                SkillChip(skill: 'Git'),
                SkillChip(skill: 'UI/UX'),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
