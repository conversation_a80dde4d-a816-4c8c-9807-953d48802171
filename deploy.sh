#!/bin/bash

# DevFolio Deployment Script for Vercel

echo "🚀 Starting DevFolio deployment process..."

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter is not installed. Please install Flutter first."
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "pubspec.yaml" ]; then
    echo "❌ pubspec.yaml not found. Please run this script from the project root."
    exit 1
fi

echo "📦 Getting Flutter dependencies..."
flutter pub get

echo "🧹 Cleaning previous builds..."
flutter clean

echo "🔧 Building for web..."
flutter build web --release --base-href /

# Check if build was successful
if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
    echo "📁 Build output is in: build/web"
    
    # Check if Vercel CLI is installed
    if command -v vercel &> /dev/null; then
        echo "🌐 Deploying to Vercel..."
        vercel --prod
    else
        echo "⚠️  Vercel CLI not found. Install it with: npm i -g vercel"
        echo "📋 Manual deployment steps:"
        echo "   1. Install Vercel CLI: npm i -g vercel"
        echo "   2. Run: vercel --prod"
        echo "   3. Or upload build/web folder to your hosting provider"
    fi
else
    echo "❌ Build failed. Please check the errors above."
    exit 1
fi

echo "🎉 Deployment process completed!"
echo "🔗 Your app should be available at your Vercel domain"
