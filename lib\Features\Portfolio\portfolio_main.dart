import 'package:devfolio/config/cubit/portfolio_cubit.dart';
import 'package:devfolio/Core/layout/responsive_layout.dart';
import 'package:devfolio/Features/Portfolio/about_screen.dart';
import 'package:devfolio/Features/Portfolio/contact_screen.dart';
import 'package:devfolio/Features/Portfolio/experience_screen.dart';
import 'package:devfolio/Features/Portfolio/home_screen.dart';
import 'package:devfolio/Features/Portfolio/projects_screen.dart';
import 'package:devfolio/Features/Portfolio/skills_screen.dart';
import 'package:devfolio/Core/Utils/widgets/animated_background.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class PortfolioMain extends StatefulWidget {
  const PortfolioMain({super.key});

  @override
  State<PortfolioMain> createState() => _PortfolioMainState();
}

class _PortfolioMainState extends State<PortfolioMain> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    final double scrollPosition = _scrollController.position.pixels;
    final double screenHeight = 1.sh;

    if (scrollPosition < screenHeight * 0.5) {
      context.read<PortfolioCubit>().setCurrentSection(0);
    } else if (scrollPosition < screenHeight * 1.5) {
      context.read<PortfolioCubit>().setCurrentSection(1);
    } else if (scrollPosition < screenHeight * 2.5) {
      context.read<PortfolioCubit>().setCurrentSection(2);
    } else if (scrollPosition < screenHeight * 3.5) {
      context.read<PortfolioCubit>().setCurrentSection(3);
    } else if (scrollPosition < screenHeight * 4.5) {
      context.read<PortfolioCubit>().setCurrentSection(4);
    } else {
      context.read<PortfolioCubit>().setCurrentSection(5);
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollToSection(int index) {
    final double screenHeight = 1.sh;
    final double targetPosition = index * screenHeight;
    _scrollController.animateTo(
      targetPosition,
      duration: const Duration(milliseconds: 800),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Row(
          children: [
            // Side Navigation for Desktop
            if (!ResponsiveLayout.isMobile(context)) _buildSideNavigation(),
            // Main Content
            Expanded(
              child: SingleChildScrollView(
                controller: _scrollController,
                child: Column(
                  children: const [
                    HomeScreen(),
                    AboutScreen(),
                    ExperienceScreen(),
                    ProjectsScreen(),
                    SkillsScreen(),
                    ContactScreen(),
                  ],
                ),
              ),
            ),
          ],
        ),
        // Bottom Navigation for Mobile
        bottomNavigationBar: ResponsiveLayout.isMobile(context)
            ? _buildBottomNavigation()
            : null,
      ),
    );
  }

  Widget _buildSideNavigation() {
    return Container(
      width: ResponsiveLayout.getSideNavWidth(context),
      color: const Color(0xFF1A1A2E),
      child: Column(
        children: [
          SizedBox(height: 40.h),
          ..._navigationItems.map(
            (item) => _buildNavItem(item.icon, item.label, item.index),
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem(IconData icon, String label, int index) {
    return BlocBuilder<PortfolioCubit, PortfolioState>(
      builder: (context, state) {
        final bool isSelected = state.currentSection == index;
        return GestureDetector(
          onTap: () => _scrollToSection(index),
          child: Container(
            margin: EdgeInsets.symmetric(vertical: 6.h),
            padding: EdgeInsets.all(10.w),
            decoration: BoxDecoration(
              color: isSelected ? const Color(0xFF6366F1) : Colors.transparent,
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Column(
              children: [
                Icon(
                  icon,
                  color: isSelected ? Colors.white : Colors.grey,
                  size: ResponsiveLayout.getIconSize(context),
                ),
                SizedBox(height: 4.h),
                Text(
                  label,
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.grey,
                    fontSize: ResponsiveLayout.getSmallFontSize(context),
                    fontWeight: isSelected
                        ? FontWeight.w600
                        : FontWeight.normal,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildBottomNavigation() {
    return BlocBuilder<PortfolioCubit, PortfolioState>(
      builder: (context, state) {
        return BottomNavigationBar(
          currentIndex: state.currentSection,
          onTap: (index) => _scrollToSection(index),
          type: BottomNavigationBarType.fixed,
          backgroundColor: const Color(0xFF1A1A2E),
          selectedItemColor: const Color(0xFF6366F1),
          unselectedItemColor: Colors.grey,
          items: _navigationItems
              .map(
                (item) => BottomNavigationBarItem(
                  icon: Icon(item.icon),
                  label: item.label,
                ),
              )
              .toList(),
        );
      },
    );
  }

  // Navigation items data
  static const List<NavigationItem> _navigationItems = [
    NavigationItem(Icons.home, 'Home', 0),
    NavigationItem(Icons.person, 'About', 1),
    NavigationItem(Icons.work, 'Experience', 2),
    NavigationItem(Icons.code, 'Projects', 3),
    NavigationItem(Icons.school, 'Skills', 4),
    NavigationItem(Icons.contact_mail, 'Contact', 5),
  ];
}

// Navigation item data class
class NavigationItem {
  final IconData icon;
  final String label;
  final int index;

  const NavigationItem(this.icon, this.label, this.index);
}
