class PersonalInfoModel {
  final String? name;
  final String? title;
  final String? description;
  final String? email;
  final String? phone;
  final String? location;
  final String? github;
  final String? linkedin;
  final String? website;
  final String? twitter;
  final String? instagram;
  final String? facebook;
  final String? youtube;
  final String? avatar;
  final String? about;
  final String? dateOfBirth;
  final String? nationality;
  final String? languages;
  final String? interests;

  PersonalInfoModel({
    this.name,
    this.title,
    this.description,
    this.email,
    this.phone,
    this.location,
    this.github,
    this.linkedin,
    this.website,
    this.avatar,
    this.about,
    this.twitter,
    this.instagram,
    this.facebook,
    this.youtube,
    this.dateOfBirth,
    this.nationality,
    this.languages,
    this.interests,
  });

  factory PersonalInfoModel.fromJson(Map<String, dynamic> json) {
    return PersonalInfoModel(
      name: json['name'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      location: json['location'] ?? '',
      github: json['github'] ?? '',
      linkedin: json['linkedin'] ?? '',
      website: json['website'] ?? '',
      avatar: json['avatar'] ?? '',
      about: json['about'] ?? '',
      twitter: json['twitter'] ?? '',
      instagram: json['instagram'] ?? '',
      facebook: json['facebook'] ?? '',
      youtube: json['youtube'] ?? '',
      dateOfBirth: json['dateOfBirth'] ?? '',
      nationality: json['nationality'] ?? '',
      languages: json['languages'] ?? '',
      interests: json['interests'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (name != null) 'name': name,
      if (title != null) 'title': title,
      if (description != null) 'description': description,
      if (email != null) 'email': email,
      if (phone != null) 'phone': phone,
      if (location != null) 'location': location,
      if (github != null) 'github': github,
      if (linkedin != null) 'linkedin': linkedin,
      if (website != null) 'website': website,
      if (avatar != null) 'avatar': avatar,
      if (about != null) 'about': about,
      if (twitter != null) 'twitter': twitter,
      if (instagram != null) 'instagram': instagram,
      if (facebook != null) 'facebook': facebook,
      if (youtube != null) 'youtube': youtube,
      if (dateOfBirth != null) 'dateOfBirth': dateOfBirth,
      if (nationality != null) 'nationality': nationality,
      if (languages != null) 'languages': languages,
      if (interests != null) 'interests': interests,
    };
  }

  PersonalInfoModel copyWith({String? key, String? value}) {
    return PersonalInfoModel(
      name: key == 'name' ? value : name,
      title: key == 'title' ? value : title,
      description: key == 'description' ? value : description,
      email: key == 'email' ? value : email,
      phone: key == 'phone' ? value : phone,
      location: key == 'location' ? value : location,
      github: key == 'github' ? value : github,
      linkedin: key == 'linkedin' ? value : linkedin,
      website: key == 'website' ? value : website,
      avatar: key == 'avatar' ? value : avatar,
      about: key == 'about' ? value : about,
      twitter: key == 'twitter' ? value : twitter,
      instagram: key == 'instagram' ? value : instagram,
      facebook: key == 'facebook' ? value : facebook,
      youtube: key == 'youtube' ? value : youtube,
      dateOfBirth: key == 'dateOfBirth' ? value : dateOfBirth,
      nationality: key == 'nationality' ? value : nationality,
      languages: key == 'languages' ? value : languages,
      interests: key == 'interests' ? value : interests,
    );
  }
}
