import 'package:flutter_bloc/flutter_bloc.dart';
import '../Models/auth_model.dart';
import '../auth_source.dart';
import 'auth_state.dart';

class AuthCubit extends Cubit<AuthState> {
  AuthCubit() : super(AuthState());

  void clearState() => emit(AuthState());

  Future<void> login(AuthModel loginModel) async {
    emit(state.copyWith(isLoading: true));
    final result = await AuthSource.login(loginModel);
    result.fold(
      (l) =>
          emit(state.copyWith(isLoading: false, message: l, isSuccess: false)),
      (r) => emit(
        state.copyWith(
          isLoading: false,
          message: 'Login successful!',
          portfolioData: r,
          isSuccess: true,
        ),
      ),
    );
  }

  Future<void> register(AuthModel registerModel) async {
    emit(state.copyWith(isLoading: true,));
    final result = await AuthSource.register(registerModel);
    result.fold(
      (l) =>
          emit(state.copyWith(isLoading: false, message: l, isSuccess: false)),
      (r) => emit(
        state.copyWith(
          isLoading: false,
          message: 'Registration successful!',
          portfolioData: r,
          isSuccess: true,
        ),
      ),
    );
  }
}
