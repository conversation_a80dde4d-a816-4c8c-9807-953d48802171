 // must be initated with supabase
class InfoModel {
  final String id;
  final String name;
  final String title;
  final String description;
  final String email;
  final String phone;
  final String location;
  final String about;
  final String avatar;
  final String password;

  InfoModel({
    required this.id,
    required this.name,
    required this.title,
    required this.description,
    required this.email,
    required this.phone,
    required this.location,
    required this.about,
    required this.avatar,
    required this.password,
  });

  factory InfoModel.fromJson(Map<String, dynamic> json) {
    return InfoModel(
      id: json['id'],
      name: json['name'],
      title: json['title'],
      description: json['description'],
      email: json['email'],
      phone: json['phone'],
      location: json['location'],
      about: json['about'],
      avatar: json['avatar'],
      password: json['password'],
    );
  }
  Map<String, dynamic> toJson({String ?  passwordIni , String ? emailIni , String ? phoneIni , String ? nameIni }) {
    return {
      'id': id,
      'name': nameIni ?? name,
      'title': title,
      'description': description,
      'email': email,
      'phone': phone,
      'location': location,
      'about': about,
      'avatar': avatar,
      'password': password,
    };
  }

  static InfoModel empty() {
    return InfoModel(
      id: '-1',
      name: 'Guest',
      title: 'Software Engineer',
      description: 'I am a software engineer with a passion for building web applications.',
      email: '<EMAIL>',
      phone: '+1234567890',
      location: 'New York, NY',
      about: 'I am a software engineer with a passion for building web applications.',
      avatar: 'assets/images/avatar.jpg',
      password: '123456',
    );
  }
}