import 'package:devfolio/Core/Utils/Reusable/custom_button.dart';
import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:devfolio/Core/Utils/Reusable/section_container.dart';
import 'package:flutter/material.dart';
import '../../Core/layout/responsive_layout.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return SectionContainer(
      child: Center(
        child: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Profile Image
              CircleAvatar(
                radius: ResponsiveLayout.getAvatarSize(context) / 2,
                backgroundColor: const Color(0xFF6366F1),
                child: Icon(
                  Icons.person,
                  size: ResponsiveLayout.getLargeIconSize(context),
                  color: Colors.white,
                ),
              ),
              SizedBox(height: ResponsiveLayout.getSectionSpacing(context)),

              // Name
              CustomText(
                text: '<PERSON>',
                fontSize: ResponsiveLayout.getTitleFontSize(context),
                fontWeight: FontWeight.bold,
                textAlign: TextAlign.center,
              ),

              SizedBox(height: ResponsiveLayout.getSmallSpacing(context)),

              // Title
              CustomText(
                text: 'Flutter Developer',
                fontSize: ResponsiveLayout.getSubtitleFontSize(context),
                color: const Color(0xFF6366F1),
                fontWeight: FontWeight.w600,
                textAlign: TextAlign.center,
              ),

              SizedBox(height: ResponsiveLayout.getSectionSpacing(context)),

              // Description
              CustomText(
                text:
                    'Passionate about creating beautiful and functional mobile applications with Flutter. Experienced in cross-platform development and modern UI/UX design.',
                fontSize: ResponsiveLayout.getBodyFontSize(context),
                color: Colors.grey,
                textAlign: TextAlign.center,
                maxLines: 4,
              ),

              SizedBox(height: ResponsiveLayout.getLargeSpacing(context)),

              // Action Buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CustomButton(
                    text: 'View Projects',
                    onPressed: () {
                      // Navigate to projects section
                    },
                  ),
                  SizedBox(width: ResponsiveLayout.getMediumSpacing(context)),
                  CustomButton(
                    text: 'Contact Me',
                    onPressed: () {
                      // Navigate to contact section
                    },
                    backgroundColor: Colors.transparent,
                    textColor: const Color(0xFF6366F1),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
