class ProjectModel {
    final String? id;
  final String? title;
  final String? description;
  final String? image;
  final List<String>? technologies;
  final String? github;
  final String? liveUrl;
  final String? category;

  ProjectModel({
    this.id,
    this.title,
    this.description,
    this.image,
    this.technologies,
    this.github,
    this.liveUrl,
    this.category,
  });

  factory ProjectModel.fromJson(Map<String, dynamic> json) {
    return ProjectModel(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      image: json['image'],
      technologies: (json['technologies'] as List?)
          ?.map((e) => e.toString())
          .toList(),
      github: json['github'],
      liveUrl: json['liveUrl'],
      category: json['category'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      if (title != null) 'title': title,
      if (description != null) 'description': description,
      if (image != null) 'image': image,
      if (technologies != null) 'technologies': technologies,
      if (github != null) 'github': github,
      if (liveUrl != null) 'liveUrl': liveUrl,
      if (category != null) 'category': category,
    };
  }
}
