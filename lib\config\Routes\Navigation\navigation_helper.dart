import 'dart:developer';
import '../../../Core/Storage/Local/UserDataService/user_data_service.dart';
import '../app_routes.dart';
import 'navigation_service.dart';

class NavigationHelper {
  static final AppNavigationService _navigationService = AppNavigationService();
  
  /// Handle navigation based on username in URL
  /// If username matches current user -> Dashboard
  /// If username is different or no user logged in -> Portfolio
  static Future<void> handleUsernameRoute(String username, {bool forcePortfolio = false}) async {
    try {
      final userData = UserDataService.getUserData();
      
      if (userData != null && userData['emailUser'] != null && !forcePortfolio) {
        final currentUserEmail = userData['emailUser'] as String;
        final currentUsername = currentUserEmail.split('@').first;
        
        if (username == currentUsername) {
          // Same user - go to dashboard
          log('Same user detected: $username, navigating to dashboard');
          final route = AppRoutes.dashboardWithUser(currentUserEmail);
          await _navigationService.clearAndNavigateTo(route);
          return;
        }
      }
      
      // Different user or no user logged in - go to portfolio
      log('Different user or no login: $username, navigating to portfolio');
      final route = AppRoutes.portfolioByUsername(username);
      await _navigationService.clearAndNavigateTo(route);
      
    } catch (e) {
      log('Error in handleUsernameRoute: $e');
      // Fallback to portfolio
      final route = AppRoutes.portfolioByUsername(username);
      await _navigationService.clearAndNavigateTo(route);
    }
  }
  
  /// Navigate to dashboard with current user's username
  static Future<void> navigateToDashboardWithUser() async {
    try {
      final userData = UserDataService.getUserData();
      if (userData != null && userData['emailUser'] != null) {
        final userEmail = userData['emailUser'] as String;
        final route = AppRoutes.dashboardWithUser(userEmail);
        await _navigationService.clearAndNavigateTo(route);
      } else {
        // Fallback to regular dashboard
        await _navigationService.clearAndNavigateTo(AppRoutes.dashboard);
      }
    } catch (e) {
      log('Error navigating to dashboard with user: $e');
      // Fallback to regular dashboard
      await _navigationService.clearAndNavigateTo(AppRoutes.dashboard);
    }
  }
  
  /// Navigate to portfolio with current user's username
  static Future<void> navigateToPortfolioWithUser() async {
    try {
      final userData = UserDataService.getUserData();
      if (userData != null && userData['emailUser'] != null) {
        final userEmail = userData['emailUser'] as String;
        final route = AppRoutes.portfolioWithUser(userEmail);
        await _navigationService.clearAndNavigateTo(route);
      } else {
        // Fallback to regular portfolio
        await _navigationService.clearAndNavigateTo(AppRoutes.portfolio);
      }
    } catch (e) {
      log('Error navigating to portfolio with user: $e');
      // Fallback to regular portfolio
      await _navigationService.clearAndNavigateTo(AppRoutes.portfolio);
    }
  }
  
  /// Check if current route matches current user
  static bool isCurrentUserRoute(String route) {
    try {
      final userData = UserDataService.getUserData();
      if (userData != null && userData['emailUser'] != null) {
        final currentUserEmail = userData['emailUser'] as String;
        return AppRoutes.isCurrentUserRoute(route, currentUserEmail);
      }
      return false;
    } catch (e) {
      log('Error checking current user route: $e');
      return false;
    }
  }
  
  /// Get current user's username
  static String? getCurrentUsername() {
    try {
      final userData = UserDataService.getUserData();
      if (userData != null && userData['emailUser'] != null) {
        final userEmail = userData['emailUser'] as String;
        return userEmail.split('@').first;
      }
      return null;
    } catch (e) {
      log('Error getting current username: $e');
      return null;
    }
  }
}
