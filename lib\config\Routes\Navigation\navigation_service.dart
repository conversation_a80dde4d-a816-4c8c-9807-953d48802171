import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:web/web.dart' as web;
import 'navigation_contract.dart';

class AppNavigationService implements NavigationContract {
  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  List<String> navigationHistory = [];

  @override
  Future<void> navigateTo(String route, {Object? arguments}) async {
    updateBrowserUrl(route);
    await navigatorKey.currentState?.pushNamed(route, arguments: arguments);
  }

  @override
  Future<void> replaceWith(String route, {Object? arguments}) async {
    if (navigationHistory.isNotEmpty) {
      navigationHistory.removeLast();
    }
    updateBrowserUrl(route);

    await navigatorKey.currentState?.pushReplacementNamed(
      route,
      arguments: arguments,
    );
  }

  @override
  Future<void> clearAndNavigateTo(String route, {Object? arguments}) async {
    navigationHistory.clear();
    updateBrowserUrl(route);

    await navigatorKey.currentState?.pushNamedAndRemoveUntil(
      route,
      (Route<dynamic> r) => false,
      arguments: arguments,
    );
  }

  @override
  Future<void> goBack() async {
    log('navigationHistory 1: ${navigationHistory.toList()}');
    if (navigationHistory.isNotEmpty) {
      navigationHistory.removeLast();
    }
    log('navigationHistory 2: ${navigationHistory.toList()}');

    if (navigationHistory.isNotEmpty) {
      _updateBrowserUrlImmediately(navigationHistory.last);
    }
    if (navigatorKey.currentState?.canPop() ?? false) {
      navigatorKey.currentState?.pop();
    }
  }

  void _updateBrowserUrlImmediately(String route) {
    try {
      final uri = Uri.base.replace(path: route);
      web.window.history.replaceState(null, '', uri.toString());
    } catch (e) {
      log('Error updating browser URL: $e');
    }
  }

  void updateBrowserUrl(String route) {
    Future.microtask(() {
      navigationHistory.add(route);
      _updateBrowserUrlImmediately(route);
    });
  }
}
