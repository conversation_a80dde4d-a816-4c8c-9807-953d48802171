import 'dart:developer';

import 'package:devfolio/Core/models/personal_info_model.dart';

import '../../../../Core/Storage/Local/UserDataService/user_data_base_service.dart';
import '../../../../Core/Storage/Local/UserDataService/user_data_service.dart';
import '../../../../Core/Storage/Local/local_storage_keys.dart';
import '../../../../Core/services/Subabase/class_tables.dart';
import '../../../../Core/services/Subabase/subabase_services.dart';
import 'package:dartz/dartz.dart';

class PersonalSource {
  
  static Future<Either<String, PersonalInfoModel>> getPersonalInfo() async {
    try {
      final id = UserDataBaseService.getUserDataId();
      log('id: $id');
      final response = await SubabaseServices.getMapField(
        table: ClassTables.portfolioData,
        fieldName: ClassTables.personalInfo,
        matchColumn: 'userId',
        matchValue: id,
      );
      if (response.status) {
        final data = PersonalInfoModel.fromJson(response.data);
        return Right(data);
      } else {
        return Left(response.message);
      }
    } catch (e) {
      return Left(e.toString());
    }
  }

  static Future<Either<String, PersonalInfoModel>> updatePersonalInfo(
    PersonalInfoModel personalInfo,
    String id,
  ) async {
    try {
      final response = await SubabaseServices.updateMapField(
        table: ClassTables.portfolioData,
        fieldName: ClassTables.personalInfo,
        newMap: personalInfo.toJson(),
        matchColumn: 'userId',
        matchValue:id,
      );
      log("personalInfo Updated response: ${response.data}");
      if (response.status) {
        final data = PersonalInfoModel.fromJson(response.data);
        UserDataService.updateMapField(LocalStorageKeys.personalInfo, data.toJson());
        return Right(data);
      } else {
        return Left(response.message);
      }
    } catch (e) {
      return Left(e.toString());
    }
  }

  static Future<Either<String, PersonalInfoModel>> savePersonalInfo(
    PersonalInfoModel personalInfo,
    String id,
  ) async {
    try {
      final response = await SubabaseServices.insertMapField(
        table: ClassTables.portfolioData,
        fieldName: ClassTables.personalInfo,
        newMap: personalInfo.toJson(),
        matchColumn: 'userId',
        matchValue:id,
      );
      if (response.status) {
        final data = PersonalInfoModel.fromJson(response.data);
        UserDataService.updateMapField(LocalStorageKeys.personalInfo, data.toJson());
        return Right(data);
      } else {
        return Left(response.message);
      } 
    } catch (e) { 
      return Left(e.toString());
    }
  }
}
