import 'package:devfolio/Core/Utils/Reusable/custom_button.dart';
import 'package:devfolio/Core/Utils/Reusable/custom_card.dart';
import 'package:devfolio/Core/Utils/Reusable/custom_icon.dart';
import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:devfolio/Core/Utils/Reusable/section_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../Core/layout/responsive_layout.dart';

class ContactScreen extends StatelessWidget {
  const ContactScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return SectionContainer(
      title: 'Contact',
      child: SingleChildScrollView(
        child: Column(
          children: [
            // Contact Information
            CustomText(
              text: 'Get In Touch',
              fontSize: ResponsiveLayout.getSubtitleFontSize(context),
              fontWeight: FontWeight.bold,
            ),

            SizedBox(height: ResponsiveLayout.getMediumSpacing(context)),

            CustomText(
              text:
                  'I\'m always open to discussing new opportunities, interesting projects, or creative ideas.',
              fontSize: ResponsiveLayout.getBodyFontSize(context),
              color: Colors.grey,
              textAlign: TextAlign.center,
            ),

            SizedBox(height: ResponsiveLayout.getSectionSpacing(context)),

            // Contact Methods
            _buildContactMethod(
              context,
              'Email',
              '<EMAIL>',
              Icons.email,
              () {
                // Launch email
              },
            ),

            SizedBox(height: ResponsiveLayout.getMediumSpacing(context)),

            _buildContactMethod(
              context,
              'LinkedIn',
              'linkedin.com/in/johndoe',
              Icons.link,
              () {
                // Launch LinkedIn
              },
            ),

            SizedBox(height: ResponsiveLayout.getMediumSpacing(context)),

            _buildContactMethod(
              context,
              'GitHub',
              'github.com/johndoe',
              Icons.code,
              () {
                // Launch GitHub
              },
            ),

            SizedBox(height: ResponsiveLayout.getSectionSpacing(context)),

            // Contact Form
            CustomText(
              text: 'Send Message',
              fontSize: ResponsiveLayout.getSubtitleFontSize(context),
              fontWeight: FontWeight.bold,
            ),

            SizedBox(height: ResponsiveLayout.getMediumSpacing(context)),

            _buildContactForm(context),
          ],
        ),
      ),
    );
  }

  Widget _buildContactMethod(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: CustomCard(
        child: Row(
          children: [
            CustomIcon(
              icon: icon,
              size: ResponsiveLayout.getLargeIconSize(context),
              color: const Color(0xFF6366F1),
            ),
            SizedBox(width: ResponsiveLayout.getMediumSpacing(context)),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomText(
                    text: title,
                    fontSize: ResponsiveLayout.getSubtitleFontSize(context),
                    fontWeight: FontWeight.bold,
                  ),
                  SizedBox(height: ResponsiveLayout.getSmallSpacing(context)),
                  CustomText(
                    text: value,
                    fontSize: ResponsiveLayout.getSmallFontSize(context),
                    color: Colors.grey,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactForm(BuildContext context) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Name Field
          CustomText(
            text: 'Name',
            fontSize: ResponsiveLayout.getSmallFontSize(context),
            fontWeight: FontWeight.w600,
          ),
          SizedBox(height: ResponsiveLayout.getSmallSpacing(context)),
          TextField(
            style: TextStyle(
              color: Colors.white,
              fontSize: ResponsiveLayout.getBodyFontSize(context),
            ),
            decoration: InputDecoration(
              hintText: 'Your Name',
              hintStyle: TextStyle(color: Colors.grey),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(
                  ResponsiveLayout.getBorderRadius(context),
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(
                  ResponsiveLayout.getBorderRadius(context),
                ),
                borderSide: BorderSide(color: Colors.grey),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(
                  ResponsiveLayout.getBorderRadius(context),
                ),
                borderSide: BorderSide(color: const Color(0xFF6366F1)),
              ),
            ),
          ),

          SizedBox(height: ResponsiveLayout.getMediumSpacing(context)),

          // Email Field
          CustomText(
            text: 'Email',
            fontSize: ResponsiveLayout.getSmallFontSize(context),
            fontWeight: FontWeight.w600,
          ),
          SizedBox(height: ResponsiveLayout.getSmallSpacing(context)),
          TextField(
            style: TextStyle(
              color: Colors.white,
              fontSize: ResponsiveLayout.getBodyFontSize(context),
            ),
            decoration: InputDecoration(
              hintText: '<EMAIL>',
              hintStyle: TextStyle(color: Colors.grey),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(
                  ResponsiveLayout.getBorderRadius(context),
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(
                  ResponsiveLayout.getBorderRadius(context),
                ),
                borderSide: BorderSide(color: Colors.grey),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(
                  ResponsiveLayout.getBorderRadius(context),
                ),
                borderSide: BorderSide(color: const Color(0xFF6366F1)),
              ),
            ),
          ),

          SizedBox(height: ResponsiveLayout.getMediumSpacing(context)),

          // Message Field
          CustomText(
            text: 'Message',
            fontSize: ResponsiveLayout.getSmallFontSize(context),
            fontWeight: FontWeight.w600,
          ),
          SizedBox(height: ResponsiveLayout.getSmallSpacing(context)),
          TextField(
            maxLines: 4,
            style: TextStyle(
              color: Colors.white,
              fontSize: ResponsiveLayout.getBodyFontSize(context),
            ),
            decoration: InputDecoration(
              hintText: 'Your message...',
              hintStyle: TextStyle(color: Colors.grey),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(
                  ResponsiveLayout.getBorderRadius(context),
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(
                  ResponsiveLayout.getBorderRadius(context),
                ),
                borderSide: BorderSide(color: Colors.grey),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(
                  ResponsiveLayout.getBorderRadius(context),
                ),
                borderSide: BorderSide(color: const Color(0xFF6366F1)),
              ),
            ),
          ),

          SizedBox(height: ResponsiveLayout.getSectionSpacing(context)),

          // Send Button
          Center(
            child: CustomButton(
              text: 'Send Message',
              onPressed: () {
                // Handle form submission
              },
              width: 200.w,
            ),
          ),
        ],
      ),
    );
  }
}
