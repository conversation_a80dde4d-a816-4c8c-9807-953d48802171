import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'Core/Storage/Local/UserDataService/user_data_service.dart';
import 'Core/resources/resources.dart';
import 'Features/Auth/Data/Cubit/auth_cubit.dart';
import 'config/Routes/app_routes.dart';
import 'config/Routes/route_generator.dart';
import 'config/Themes/themes_app.dart';
import 'config/cubit/admin_cubit.dart';
import 'main.dart';

class AuthApp extends StatelessWidget {
  const AuthApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(1920, 1080),
      minTextAdapt: true,
      splitScreenMode: true,
      useInheritedMediaQuery: true,
      enableScaleWH: () => false,
      enableScaleText: () => true,
      builder: (context, child) {
        return MultiBlocProvider(
          providers: [
            BlocProvider<AuthCubit>(create: (context) => AuthCubit()),
            BlocProvider<AdminCubit>(create: (context) => AdminCubit()),
          ],
          child: MaterialApp(
            title: AppStrings.appTitle,
            debugShowCheckedModeBanner: false,
            theme: themesApp(context),
            initialRoute: getRoute(),
            onGenerateRoute: RouteGenerator.generateRoute,
            navigatorKey: kNavigationService.navigatorKey,
          ),
        );
      },
    );
  }
}

String getRoute() {
  final bool userData = UserDataService.hasUserData();
  if (userData) {
    kNavigationService.updateBrowserUrl(AppRoutes.dashboard);
    return AppRoutes.dashboard;
  } else {
    kNavigationService.updateBrowserUrl(AppRoutes.login);
    return AppRoutes.login;
  }
}
