import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'Core/Storage/Local/UserDataService/user_data_service.dart';
import 'Core/resources/resources.dart';
import 'Features/Auth/Data/Cubit/auth_cubit.dart';
import 'config/Routes/app_routes.dart';
import 'config/Routes/route_generator.dart';
import 'config/Themes/themes_app.dart';
import 'config/cubit/admin_cubit.dart';
import 'main.dart';

class AuthApp extends StatelessWidget {
  const AuthApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(1920, 1080),
      minTextAdapt: true,
      splitScreenMode: true,
      useInheritedMediaQuery: true,
      enableScaleWH: () => false,
      enableScaleText: () => true,
      builder: (context, child) {
        return MultiBlocProvider(
          providers: [
            BlocProvider<AuthCubit>(create: (context) => AuthCubit()),
            BlocProvider<AdminCubit>(create: (context) => AdminCubit()),
          ],
          child: MaterialApp(
            title: AppStrings.appTitle,
            debugShowCheckedModeBanner: false,
            theme: themesApp(context),
            initialRoute: kIsWeb ? getRoute() : getRoute(),
            onGenerateRoute: RouteGenerator.generateRoute,
            navigatorKey: kNavigationService.navigatorKey,
          ),
        );
      },
    );
  }
}

String getRoute() {
  try {
    final bool hasUserData = UserDataService.hasUserData();

    if (hasUserData) {
      // Get user data to create personalized URL
      final userData = UserDataService.getUserData();
      if (userData != null && userData['emailUser'] != null) {
        final userEmail = userData['emailUser'] as String;
        final route = AppRoutes.dashboardWithUser(userEmail);

        // Only update browser URL on web and after a delay to avoid history issues
        if (kIsWeb) {
          Future.delayed(const Duration(milliseconds: 100), () {
            kNavigationService.updateBrowserUrl(route);
          });
        }

        return route;
      } else {
        // Fallback to regular dashboard if no email found
        return AppRoutes.dashboard;
      }
    } else {
      return AppRoutes.login;
    }
  } catch (e) {
    // Fallback to login route if there's any error
    return AppRoutes.login;
  }
}
