import 'package:flutter_bloc/flutter_bloc.dart';

// Portfolio State
class PortfolioState {
  final int currentSection;
  final bool isLoading;

  PortfolioState({this.currentSection = 0, this.isLoading = false});

  PortfolioState copyWith({int? currentSection, bool? isLoading}) {
    return PortfolioState(
      currentSection: currentSection ?? this.currentSection,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}

// Portfolio Cubit
class PortfolioCubit extends Cubit<PortfolioState> {
  PortfolioCubit() : super(PortfolioState());

  void setCurrentSection(int section) {
    emit(state.copyWith(currentSection: section));
  }

  void setLoading(bool loading) {
    emit(state.copyWith(isLoading: loading));
  }

  void nextSection() {
    final nextSection = (state.currentSection + 1) % 6;
    emit(state.copyWith(currentSection: nextSection));
  }

  void previousSection() {
    final previousSection = (state.currentSection - 1 + 6) % 6;
    emit(state.copyWith(currentSection: previousSection));
  }
}
