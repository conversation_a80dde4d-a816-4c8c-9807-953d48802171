import 'dart:developer';
import 'package:dartz/dartz.dart';
import '../Storage/Local/UserDataService/user_data_service.dart';
import '../models/portfolio_data_model.dart';
import 'Subabase/subabase_services.dart';

class UserVerificationService {
  
  /// Check if user exists by username (part before @)
  /// Returns the full user data if found, null if not found
  static Future<Either<String, PortfolioDataModel?>> checkUserByUsername(String username) async {
    try {
      log('Checking user by username: $username');
      
      // First, try to find user in database by searching for emails that start with username
      final response = await SubabaseServices.get(
        table: 'portfolio_data',
        filter: {}, // We'll search manually since we need to check email patterns
      );
      
      if (response.status && response.data != null) {
        final List<dynamic> users = response.data as List<dynamic>;
        
        // Search for user with matching username (part before @)
        for (var userData in users) {
          final userMap = userData as Map<String, dynamic>;
          final email = userMap['emailUser'] as String?;
          
          if (email != null) {
            final emailUsername = email.split('@').first;
            if (emailUsername.toLowerCase() == username.toLowerCase()) {
              log('User found in database: $email');
              final portfolioData = PortfolioDataModel.fromJson(userMap);
              log('User found in database: ${portfolioData.toJson()}');
              return Right(portfolioData);
            }
          }
        }
        
        log('User not found in database: $username');
        return Right(null); // User not found
      } else {
        log('Database query failed: ${response.message}');
        return Left(response.message);
      }
    } catch (e) {
      log('Error checking user by username: $e');
      return Left(e.toString());
    }
  }
  
  /// Check if user exists by full email
  static Future<Either<String, PortfolioDataModel?>> checkUserByEmail(String email) async {
    try {
      log('Checking user by email: $email');
      
      final response = await SubabaseServices.get(
        table: 'portfolio_data',
        filter: {'emailUser': email},
      );
      
      if (response.status && response.data != null) {
        final List<dynamic> users = response.data as List<dynamic>;
        if (users.isNotEmpty) {
          final userMap = users.first as Map<String, dynamic>;
          log('User found by email: $email');
          final portfolioData = PortfolioDataModel.fromJson(userMap);
          return Right(portfolioData);
        } else {
          log('User not found by email: $email');
          return Right(null);
        }
      } else {
        log('Database query failed: ${response.message}');
        return Left(response.message);
      }
    } catch (e) {
      log('Error checking user by email: $e');
      return Left(e.toString());
    }
  }
  
  /// Get user data for portfolio display (without sensitive info)
  static Future<Either<String, Map<String, dynamic>?>> getUserPortfolioData(String username) async {
    try {
      final result = await checkUserByUsername(username);
      
      return result.fold(
        (error) => Left(error),
        (portfolioData) {
          if (portfolioData != null) {
            // Return only public portfolio data (remove sensitive info)
            final publicData = portfolioData.toJson();
            publicData.remove('password'); // Remove password
            publicData.remove('userId'); // Remove user ID for security
            
            return Right(publicData);
          } else {
            return Right(null);
          }
        },
      );
    } catch (e) {
      log('Error getting user portfolio data: $e');
      return Left(e.toString());
    }
  }
  
  /// Check if current user matches the username in URL
  static bool isCurrentUser(String username) {
    try {
      final userData = UserDataService.getUserData();
      if (userData != null && userData['emailUser'] != null) {
        final currentUserEmail = userData['emailUser'] as String;
        final currentUsername = currentUserEmail.split('@').first;
        return currentUsername.toLowerCase() == username.toLowerCase();
      }
      return false;
    } catch (e) {
      log('Error checking if current user: $e');
      return false;
    }
  }
  
  /// Save user data to local storage after verification
  static Future<void> saveUserDataLocally(PortfolioDataModel portfolioData) async {
    try {
      await UserDataService.saveUserData(portfolioData.toJson());
      log('User data saved locally for: ${portfolioData.email}');
    } catch (e) {
      log('Error saving user data locally: $e');
    }
  }
}
