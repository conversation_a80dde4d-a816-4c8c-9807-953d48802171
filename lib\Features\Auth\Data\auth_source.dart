import 'package:dartz/dartz.dart';
import 'package:devfolio/Core/services/Subabase/subabase_services.dart';
import 'package:devfolio/Features/Auth/Data/Models/auth_model.dart';

import '../../../Core/Storage/Local/UserDataService/user_data_service.dart';
import '../../../Core/models/portfolio_data_model.dart';

class AuthSource {
  static Future<Either<String, PortfolioDataModel>> login(
    AuthModel loginModel,
  ) async {
    try {
      final response = await SubabaseServices.loginUser(
        loginModel.email,
        loginModel.password,
      );
      if (response.status) {
        final data = PortfolioDataModel.fromJson(response.data);
        await UserDataService.saveUserData(data.toJson());
        return Right(data);
      } else {
        return Left(response.message);
      }
    } catch (e) {
      return Left(e.toString());
    }
  }

  static Future<Either<String, PortfolioDataModel>> register(
    AuthModel registerModel,
  ) async {
    try {
      final response = await SubabaseServices.registerUser(registerModel);
      if (response.status) {
        final data = PortfolioDataModel.fromJson(response.data);
        await UserDataService.saveUserData(data.toJson());
        return Right(data);
      } else {
        return Left(response.message);
      }
    } catch (e) {
      return Left(e.toString());
    }
  }
}
