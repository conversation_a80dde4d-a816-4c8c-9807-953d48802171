import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:hive_flutter/adapters.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'Core/Storage/Local/UserDataService/user_data_base_service.dart';
import 'Core/services/Subabase/subabase_services.dart';
import 'auth_app.dart';
import 'config/Routes/Navigation/navigation_service.dart';
import 'config/subabase_keys.dart';

Future<void> main() async {
  try {
    WidgetsFlutterBinding.ensureInitialized();

    // Add error handling for web-specific issues
    if (kIsWeb) {
      // Set up error handling for web navigation issues
      FlutterError.onError = (FlutterErrorDetails details) {
        if (details.exception.toString().contains('history')) {
          // Ignore history-related errors on web
          return;
        }
        FlutterError.presentError(details);
      };
    }

    await Hive.initFlutter();
    await UserDataBaseService.init();
    await Supabase.initialize(
      url: SupabaseKeys.url,
      anonKey: SupabaseKeys.apiKey,
    );
    await SubabaseServices.init();
    runApp(const AuthApp());
  } catch (e) {
    // Fallback error handling
    runApp(
      MaterialApp(
        home: Scaffold(body: Center(child: Text('Error initializing app: $e'))),
      ),
    );
  }
}

final AppNavigationService kNavigationService = AppNavigationService();
