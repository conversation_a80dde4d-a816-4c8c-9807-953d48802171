import 'package:flutter/material.dart';
import 'package:hive_flutter/adapters.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'Core/Storage/Local/UserDataService/user_data_base_service.dart';
import 'Core/services/Subabase/subabase_services.dart';
import 'auth_app.dart';
import 'config/Routes/Navigation/navigation_service.dart';
import 'config/subabase_keys.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Hive.initFlutter();
  await UserDataBaseService.init();
  await Supabase.initialize(
    url: SupabaseKeys.url,
    anonKey: SupabaseKeys.apiKey,
  );
  await SubabaseServices.init();
  runApp(const AuthApp());
}

final AppNavigationService kNavigationService = AppNavigationService();
