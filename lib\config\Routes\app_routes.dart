import 'package:flutter/material.dart';

import '../../Features/Admin/Main_Dashboard/Presentation/Page/dashboard_page.dart';
import '../../Features/Auth/Presentation/Pages/login_page.dart';
import '../../Features/Auth/Presentation/Pages/register_page.dart';
import '../../Features/Portfolio/portfolio_main.dart';
import '../../main.dart';

class AppRoutes {
  static const String splash = '/';
  static const String login = '/login';
  static const String register = '/register';
  static const String dashboard = '/dashboard';
  static const String portfolio = '/portfolio';

  // Dynamic routes with user info
  static String dashboardWithUser(String userEmail) {
    // Extract username (part before @)
    final username = userEmail.split('@').first;
    return '/dashboard/$username';
  }

  static String portfolioWithUser(String userEmail) {
    // Extract username (part before @)
    final username = userEmail.split('@').first;
    return '/portfolio/$username';
  }

  // Route for accessing portfolio by username directly
  static String portfolioByUsername(String username) {
    return '/portfolio/$username';
  }

  // Helper method to extract username from URL
  static String? extractUsernameFromRoute(String route) {
    if (route.startsWith('/dashboard/') || route.startsWith('/portfolio/')) {
      final parts = route.split('/');
      if (parts.length >= 3) {
        return parts[2]; // Return username directly
      }
    }
    return null;
  }

  // Helper method to check if route is for current user
  static bool isCurrentUserRoute(String route, String currentUserEmail) {
    final usernameFromRoute = extractUsernameFromRoute(route);
    final currentUsername = currentUserEmail.split('@').first;
    return usernameFromRoute == currentUsername;
  }
}

class AppRouteBuilders {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    final routeName = settings.name ?? '';

    // Handle dynamic routes with username
    if (routeName.startsWith('/dashboard/')) {
      final username = AppRoutes.extractUsernameFromRoute(routeName);
      if (username != null) {
        // Check if this is the current user
        return _handleUserRoute(username, isPortfolio: false);
      }
      return _defaultPageRoute(const AdminDashboard());
    } else if (routeName.startsWith('/portfolio/')) {
      final username = AppRoutes.extractUsernameFromRoute(routeName);
      if (username != null) {
        // Always show portfolio for any username
        return _defaultPageRoute(const PortfolioMain());
      }
      return _defaultPageRoute(const PortfolioMain());
    }

    // Handle static routes
    switch (routeName) {
      case AppRoutes.login:
        return _defaultPageRoute(const LoginPage());
      case AppRoutes.register:
        return _defaultPageRoute(const RegisterPage());
      case AppRoutes.dashboard:
        return _defaultPageRoute(const AdminDashboard());
      case AppRoutes.portfolio:
        return _defaultPageRoute(const PortfolioMain());
      default:
        return _errorRoute('No route defined for "$routeName"');
    }
  }

  static Route<dynamic> _handleUserRoute(
    String username, {
    required bool isPortfolio,
  }) {
    // Handle the navigation logic based on username
    Future.microtask(() {
      kNavigationService.handleUsernameNavigation(
        username,
        forcePortfolio: isPortfolio,
      );
    });

    // Return the appropriate page immediately
    if (isPortfolio) {
      return _defaultPageRoute(const PortfolioMain());
    } else {
      return _defaultPageRoute(const AdminDashboard());
    }
  }

  static MaterialPageRoute<dynamic> _defaultPageRoute(Widget screen) {
    return MaterialPageRoute(builder: (_) => screen);
  }

  static Route<dynamic> _errorRoute(String message) {
    return MaterialPageRoute(
      builder: (_) => Scaffold(
        body: Center(
          child: Text(message, style: const TextStyle(fontSize: 18)),
        ),
      ),
    );
  }
}
