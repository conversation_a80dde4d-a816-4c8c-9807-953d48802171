import 'package:flutter/material.dart';

import '../../Features/Admin/Main_Dashboard/Presentation/Page/dashboard_page.dart';
import '../../Features/Auth/Presentation/Pages/login_page.dart';
import '../../Features/Auth/Presentation/Pages/register_page.dart';
import '../../Features/Portfolio/portfolio_main.dart';

class AppRoutes {
  static const String splash = '/';
  static const String login = '/login';
  static const String register = '/register';
  static const String dashboard = '/dashboard';
  static const String portfolio = '/portfolio';

  // Dynamic routes with user info
  static String dashboardWithUser(String userEmail) {
    // Clean email for URL (remove @ and . characters)
    final cleanEmail = userEmail
        .replaceAll('@', '-at-')
        .replaceAll('.', '-dot-');
    return '/dashboard/$cleanEmail';
  }

  static String portfolioWithUser(String userEmail) {
    final cleanEmail = userEmail
        .replaceAll('@', '-at-')
        .replaceAll('.', '-dot-');
    return '/portfolio/$cleanEmail';
  }

  // Helper method to extract email from URL
  static String? extractEmailFromRoute(String route) {
    if (route.startsWith('/dashboard/') || route.startsWith('/portfolio/')) {
      final parts = route.split('/');
      if (parts.length >= 3) {
        final cleanEmail = parts[2];
        // Convert back to original email format
        return cleanEmail.replaceAll('-at-', '@').replaceAll('-dot-', '.');
      }
    }
    return null;
  }
}

class AppRouteBuilders {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    final routeName = settings.name ?? '';

    // Handle dynamic routes
    if (routeName.startsWith('/dashboard/')) {
      return _defaultPageRoute(const AdminDashboard());
    } else if (routeName.startsWith('/portfolio/')) {
      return _defaultPageRoute(const PortfolioMain());
    }

    // Handle static routes
    switch (routeName) {
      case AppRoutes.login:
        return _defaultPageRoute(const LoginPage());
      case AppRoutes.register:
        return _defaultPageRoute(const RegisterPage());
      case AppRoutes.dashboard:
        return _defaultPageRoute(const AdminDashboard());
      case AppRoutes.portfolio:
        return _defaultPageRoute(const PortfolioMain());
      default:
        return _errorRoute('No route defined for "$routeName"');
    }
  }

  static MaterialPageRoute<dynamic> _defaultPageRoute(Widget screen) {
    return MaterialPageRoute(builder: (_) => screen);
  }

  static Route<dynamic> _errorRoute(String message) {
    return MaterialPageRoute(
      builder: (_) => Scaffold(
        body: Center(
          child: Text(message, style: const TextStyle(fontSize: 18)),
        ),
      ),
    );
  }
}
