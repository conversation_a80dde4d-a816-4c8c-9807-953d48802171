
import 'package:flutter/material.dart';

import '../../Features/Admin/Main_Dashboard/Presentation/Page/dashboard_page.dart';
import '../../Features/Auth/Presentation/Pages/login_page.dart';
import '../../Features/Auth/Presentation/Pages/register_page.dart';
import '../../Features/Portfolio/portfolio_main.dart';

class AppRoutes {
  static const String splash = '/';
  static const String login = '/login';
  static const String register = '/register';
  static const String dashboard = '/dashboard';
  static const String portfolio = '/portfolio';
  
}

class AppRouteBuilders {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
        case AppRoutes.login:
          return _defaultPageRoute(const LoginPage()); 
        case AppRoutes.register:
          return _defaultPageRoute(const RegisterPage());
        case AppRoutes.dashboard:
          return _defaultPageRoute(const AdminDashboard());
        case AppRoutes.portfolio:
          return _defaultPageRoute(const PortfolioMain());
      default:
        return _errorRoute('No route defined for "${settings.name}"');
    }
  }

  static MaterialPageRoute<dynamic> _defaultPageRoute(Widget screen) {
    return MaterialPageRoute(builder: (_) => screen);
  }


  static Route<dynamic> _errorRoute(String message) {
    return MaterialPageRoute(
      builder: (_) => Scaffold(
        body:
            Center(child: Text(message, style: const TextStyle(fontSize: 18))),
      ),
    );
  }
}
