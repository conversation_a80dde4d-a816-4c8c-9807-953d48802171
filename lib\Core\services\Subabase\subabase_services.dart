import 'dart:developer';
import 'dart:typed_data';

import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/portfolio_data_model.dart';
import 'class_tables.dart';
import 'response_model.dart';
import '../../../Features/Auth/Data/Models/auth_model.dart';

class SubabaseServices {
  static late SupabaseClient _supabase;

  static Future<void> init() async {
    _supabase = Supabase.instance.client;
  }

  static Future<ResponseModel> registerUser(AuthModel user) async {
    try {
      final emailCheck = await get(
        table: 'portfolio_data',
        filter: {'emailUser': user.email},
      );
      final emailMap = _convertToListToSingleMap(emailCheck.data);
      if (emailMap != null) {
        return ResponseModel.error(message: 'Email already exists');
      }

      final phoneCheck = await get(
        table: 'portfolio_data',
        filter: {'phone': user.phone},
      );
      final phoneMap = _convertToListToSingleMap(phoneCheck.data);
      if (phoneMap != null) {
        return ResponseModel.error(message: 'Phone already exists');
      }

      final addUser = await _supabase.auth.signUp(
        email: user.email,
        password: user.password,
      );

      if (addUser.user?.id == null) {
        return ResponseModel.error(message: 'Failed to create user account');
      }

      final portfolioData = PortfolioDataModel(
        userId: addUser.user!.id,
        email: user.email,
        phone: user.phone,
        name: user.name,
        password: user.password,
        personalInfo: null,
        skills: [],
        experience: [],
        projects: [],
        education: [],
        certifications: [],
        sendMessage: [],
      );

      final portfolioResult = await _supabase
          .from('portfolio_data')
          .insert(portfolioData.toJson())
          .select();

      final userMap = _convertToListToSingleMap(portfolioResult);
      if (userMap == null) {
        return ResponseModel.error(message: 'Failed to create user data');
      }

      return ResponseModel.success(
        message: 'Registration successful!',
        data: userMap,
      );
    } catch (e) {
      log("Registration error: $e");
      return ResponseModel.error(message: 'Registration failed', data: e.toString());
    }
  }

  static Map<String, dynamic>? _convertToListToSingleMap(dynamic result) {
    if (result is List && result.isNotEmpty) {
      return result.first as Map<String, dynamic>;
    }
    return null;
  }

  static Future<ResponseModel> loginUser(String email, String password) async {
    try {
      final authResult = await _supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );
      if (authResult.user == null) {
        return ResponseModel.error(message: 'Invalid email or password');
      }

      final userData = await get(
        table: 'portfolio_data',
        filter: {'emailUser': email, 'password': password},
      );

      final userMap = _convertToListToSingleMap(userData.data);
      if (userMap != null) {
        return ResponseModel.success(
          message: 'Login successful!',
          data: userMap,
        );
      }

      return ResponseModel.error(message: 'Invalid email or password');
    } catch (e) {
      log("Login error: $e");
      return ResponseModel.error(message: 'Login failed', data: e.toString());
    }
  }

  static Future<ResponseModel> get({
    required String table,
    Map<String, dynamic>? filter,
  }) async {
    try {
      var query = _supabase.from(table).select();
      filter?.forEach((key, value) {
        query = query.eq(key, value);
      });
      final result = await query;

      return ResponseModel.success(data: result);
    } catch (e) {
      log("Get error for $table: $e");
      return ResponseModel.error(message: 'Get failed', data: e.toString());
    }
  }

  static Future<ResponseModel> insert({
    required String table,
    required Map<String, dynamic> values,
  }) async {
    try {
      final result = await _supabase.from(table).insert(values).select();
      return ResponseModel.success(data: result);
    } catch (e) {
      return ResponseModel.error(message: 'Insert failed', data: e.toString());
    }
  }

  static Future<ResponseModel> update({
    required String table,
    required Map<String, dynamic> values,
    required String matchColumn,
    required dynamic matchValue,
  }) async {
    try {
      final result = await _supabase
          .from(table)
          .update(values)
          .eq(matchColumn, matchValue)
          .select();
      final data = _convertToListToSingleMap(result);
      return ResponseModel.success(data: data);
    } catch (e) {
      return ResponseModel.error(message: 'Update failed', data: e.toString());
    }
  }

  static Future<ResponseModel> delete({
    required String table,
    required String matchColumn,
    required dynamic matchValue,
  }) async {
    try {
      final result = await _supabase
          .from(table)
          .delete()
          .eq(matchColumn, matchValue)
          .select();
      return ResponseModel.success(data: result);
    } catch (e) {
      return ResponseModel.error(message: 'Delete failed', data: e.toString());
    }
  }

  static Future<ResponseModel> uploadImage({
    required String bucket,
    required String path,
    required Uint8List fileBytes,
    String? fileType,
  }) async {
    try {
      final response = await _supabase.storage
          .from(bucket)
          .uploadBinary(path, fileBytes, fileOptions: FileOptions(contentType: fileType));
      if (response.isNotEmpty) {
        final publicUrl = _supabase.storage.from(bucket).getPublicUrl(path);
        return ResponseModel.success(data: publicUrl);
      }
      return ResponseModel.error(message: 'Upload failed');
    } catch (e) {
      return ResponseModel.error(message: 'Upload failed', data: e.toString());
    }
  }
  static Future<ResponseModel> getPortfolioData(String email) async {
    try {
      final result = await get(table: ClassTables.portfolioData, filter: {'emailUser': email});
      final userData = _convertToListToSingleMap(result.data);
      return ResponseModel.success(data: userData);
    } catch (e) {
      return ResponseModel.error(message: 'Get portfolio data failed', data: e.toString());
    }
  }
  static Future<ResponseModel> getMapField({
    required String table,
    required String fieldName,
    required String matchColumn,
    required dynamic matchValue,
  }) async {
    try {
      final result = await get(table: table, filter: {matchColumn: matchValue});
      final userData = _convertToListToSingleMap(result.data);
      return ResponseModel.success(data: userData?[fieldName]);
    } catch (e) {
      return ResponseModel.error(message: 'Get map field failed', data: e.toString());
    }
  }
  static Future<ResponseModel> addMapToList({
    required String table,
    required String fieldName,
    required Map<String, dynamic> mapToAdd,
    required String matchColumn,
    required dynamic matchValue,
  }) async {
    try {
      final currentData = await get(table: table, filter: {matchColumn: matchValue});
      final userData = _convertToListToSingleMap(currentData.data);

      if (userData != null) {
        final currentList = List<Map<String, dynamic>>.from(userData[fieldName] ?? []);
        currentList.add(mapToAdd);

        return await update(
          table: table,
          values: {fieldName: currentList},
          matchColumn: matchColumn,
          matchValue: matchValue,
        );
      }
      return ResponseModel.error(message: 'User not found');
    } catch (e) {
      return ResponseModel.error(message: 'Add map to list failed', data: e.toString());
    }
  }

  static Future<ResponseModel> deleteMapFromList({
    required String table,
    required String fieldName,
    required String matchColumn,
    required dynamic matchValue,
    required String deleteByKey,
    required dynamic deleteByValue,
  }) async {
    try {
      final currentData = await get(table: table, filter: {matchColumn: matchValue});
      final userData = _convertToListToSingleMap(currentData.data);

      if (userData != null) {
        final currentList = List<Map<String, dynamic>>.from(userData[fieldName] ?? []);
        currentList.removeWhere((item) => item[deleteByKey] == deleteByValue);

        return await update(
          table: table,
          values: {fieldName: currentList},
          matchColumn: matchColumn,
          matchValue: matchValue,
        );
      }
      return ResponseModel.error(message: 'User not found');
    } catch (e) {
      return ResponseModel.error(message: 'Delete map from list failed', data: e.toString());
    }
  }

  static Future<ResponseModel> updateMapInList({
    required String table,
    required String fieldName,
    required String matchColumn,
    required dynamic matchValue,
    required String updateByKey,
    required dynamic updateByValue,
    required Map<String, dynamic> newMapData,
  }) async {
    try {
      final currentData = await get(table: table, filter: {matchColumn: matchValue});
      final userData = _convertToListToSingleMap(currentData.data);

      if (userData != null) {
        final currentList = List<Map<String, dynamic>>.from(userData[fieldName] ?? []);
        bool updated = false;

        for (int i = 0; i < currentList.length; i++) {
          if (currentList[i][updateByKey] == updateByValue) {
            currentList[i] = newMapData;
            updated = true;
            break;
          }
        }

        if (!updated) {
          return ResponseModel.error(message: 'Item to update not found');
        }

        return await update(
          table: table,
          values: {fieldName: currentList},
          matchColumn: matchColumn,
          matchValue: matchValue,
        );
      }
      return ResponseModel.error(message: 'User not found');
    } catch (e) {
      return ResponseModel.error(message: 'Update map in list failed', data: e.toString());
    }
  }

  static Future<ResponseModel> updateMapField({
  required String table,
  required String fieldName,
  required Map<String, dynamic> newMap,
  required String matchColumn,
  required dynamic matchValue,
}) async {
  
  try {
    final result = await update(
      table: table,
      values: {fieldName: newMap},
      matchColumn: matchColumn,
      matchValue: matchValue,
    );
    return ResponseModel.success(data: result.data);
  } catch (e) {
    return ResponseModel.error(message: 'Update map field failed', data: e.toString());
  }
}
 
  static Future<ResponseModel> insertMapField({
    required String table,
    required String fieldName,
    required Map<String, dynamic> newMap,
    required String matchColumn,
    required dynamic matchValue,
  }) async {
    try {
      final result = await _supabase.from(table).insert({fieldName: newMap}).eq(matchColumn, matchValue).select();
      return ResponseModel.success(data: result);
    } catch (e) {
      return ResponseModel.error(message: 'Insert map field failed', data: e.toString());
    }
  }

  static Future<ResponseModel> addToList({
  required String table,
  required String fieldName,
  required Map<String, dynamic> newItem,
  required String matchColumn,
  required dynamic matchValue,
}) async {
  try {
    final response = await get(
      table: table,
      filter: {matchColumn: matchValue},
    );

    final data = _convertToListToSingleMap(response.data);
    if (data == null) {
      return ResponseModel.error(message: 'User not found');
    }

    final list = List<Map<String, dynamic>>.from(data[fieldName] ?? []);
    list.add(newItem);

    return await update(
      table: table,
      values: {fieldName: list},
      matchColumn: matchColumn,
      matchValue: matchValue,
    );
  } catch (e) {
    return ResponseModel.error(message: 'Add to list failed', data: e.toString());
  }
}


static Future<ResponseModel> updateItemInList({
  required String table,
  required String fieldName,
  required String matchColumn,
  required dynamic matchValue,
  required String itemKey,
  required dynamic itemValue,
  required Map<String, dynamic> updatedItem,
}) async {
  try {
    final response = await get(
      table: table,
      filter: {matchColumn: matchValue},
    );

    final data = _convertToListToSingleMap(response.data);
    if (data == null) {
      return ResponseModel.error(message: 'User not found');
    }

    final list = List<Map<String, dynamic>>.from(data[fieldName] ?? []);
    final index = list.indexWhere((item) => item[itemKey] == itemValue);
    if (index == -1) {
      return ResponseModel.error(message: 'Item not found');
    }

    list[index] = updatedItem;

    return await update(
      table: table,
      values: {fieldName: list},
      matchColumn: matchColumn,
      matchValue: matchValue,
    );
  } catch (e) {
    return ResponseModel.error(message: 'Update item in list failed', data: e.toString());
  }
}


static Future<ResponseModel> removeFromList({
  required String table,
  required String fieldName,
  required String matchColumn,
  required dynamic matchValue,
  required String itemKey,
  required dynamic itemValue,
}) async {
  try {
    final response = await get(
      table: table,
      filter: {matchColumn: matchValue},
    );

    final data = _convertToListToSingleMap(response.data);
    if (data == null) {
      return ResponseModel.error(message: 'User not found');
    }

    final list = List<Map<String, dynamic>>.from(data[fieldName] ?? []);
    list.removeWhere((item) => item[itemKey] == itemValue);

    return await update(
      table: table,
      values: {fieldName: list},
      matchColumn: matchColumn,
      matchValue: matchValue,
    );
  } catch (e) {
    return ResponseModel.error(message: 'Remove from list failed', data: e.toString());
  }
}


}
