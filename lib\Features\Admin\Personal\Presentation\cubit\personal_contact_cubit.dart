import 'package:devfolio/Features/Admin/Personal/Presentation/cubit/personal_contact_state.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../Data/personal_source.dart';
import '../../../../../Core/models/personal_info_model.dart';
import '../../../../../Core/Storage/Local/UserDataService/user_data_base_service.dart';

class PersonalContactCubit extends Cubit<PersonalContactState> {
  PersonalContactCubit() : super(PersonalContactState());
  Future<void> initPersonalContactData() async {
    emit(state.copyWith(isLoading: true, errorMessage: null));
    final result = await PersonalSource.getPersonalInfo();
    result.fold(
      (l) => emit(state.copyWith(errorMessage: l, isLoading: false)),
      (r) => emit(
        state.copyWith(personalData: r, isLoading: false, currentData: r),
      ),
    );
  }

  Future<void> savePersonalContactData() async {
    emit(state.copyWith(isLoading: true, errorMessage: null));
    final userId = UserDataBaseService.getUserDataId();
    final result = await PersonalSource.updatePersonalInfo(
      state.personalData!,
      userId,
    );
    result.fold(
      (l) => emit(state.copyWith(errorMessage: l, isLoading: false)),
      (r) => emit(
        state.copyWith(
          personalData: r,
          isLoading: false,
          hasChanges: false,
          currentData: r,
        ),
      ),
    );
  }

  // Set current tab
  void setCurrentTab(int tabIndex) {
    emit(state.copyWith(currentTab: tabIndex));
  }

  void updateField(String key, String value) {
    final currentData = state.personalData;
    if (currentData == null) return;

    final map = currentData.toJson();
    if (map[key] == value) return;

    final updatedData = currentData.copyWith(key: key, value: value);
    emit(state.copyWith(personalData: updatedData));
    hasChanged();
  }

  // Reset state
  void reset() {
    emit(PersonalContactState());
  }

  void hasChanged() {
    final originalMap = state.currentData?.toJson() ?? {};
    final currentMap = state.personalData?.toJson() ?? {};
    final hasChanged = !mapEquals(originalMap, currentMap);
    emit(state.copyWith(hasChanges: hasChanged));
  }

  // Getters
  int get currentTab => state.currentTab;
  bool get isLoading => state.isLoading;
  String? get errorMessage => state.errorMessage;
  PersonalInfoModel? get personalData => state.personalData;
}
