
import 'package:devfolio/Features/Admin/Personal/Presentation/cubit/personal_contact_state.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../Data/personal_source.dart';
import '../../../../../Core/models/personal_info_model.dart';

class PersonalContactCubit extends Cubit<PersonalContactState> {
  PersonalContactCubit() : super(PersonalContactState());
  String id = '';
  Future<void> initPersonalContactData(

  ) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));
    final result = await PersonalSource.getPersonalInfo();
    result.fold(
      (l) => emit(state.copyWith(errorMessage: l, isLoading: false)),
      (r) => emit(state.copyWith(personalData: r, isLoading: false, currentData: r)),
    );
  }

  Future<void> savePersonalContactData() async {
    emit(state.copyWith(isLoading: true, errorMessage: null));
    final result = await PersonalSource.updatePersonalInfo(
      state.personalData!,
      id,
    );
    result.fold(
      (l) => emit(state.copyWith(errorMessage: l, isLoading: false)),
      (r) => emit(
        state.copyWith(personalData: r, isLoading: false, hasChanges: false),
      ),
    );
  }

  // Set current tab
  void setCurrentTab(int tabIndex) {
    emit(state.copyWith(currentTab: tabIndex));
  }

  void updateField(String key, String value) {
    final map = state.personalData?.toJson() ?? {};
    final data =state.personalData;
    if (map[key] == value) return;
    map[key] = value;

    emit(state.copyWith(personalData: data?.copyWith(key: value)));
    hasChanged();
  }

  // Reset state
  void reset() {
    emit(PersonalContactState());
  }

  void hasChanged() {
    final map = state.currentData?.toJson() ?? {};
    final originalMap = state.personalData?.toJson() ?? {};
    final hasChanged = !mapEquals(map, originalMap);
    emit(state.copyWith(hasChanges: hasChanged));
  }

  // Getters
  int get currentTab => state.currentTab;
  bool get isLoading => state.isLoading;
  String? get errorMessage => state.errorMessage;
  PersonalInfoModel? get personalData => state.personalData;
}
